<script setup lang="tsx">
import { onMounted, onUnmounted, shallowRef, ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import type { ColumnDefinition, RowAction, PoolStock, QueryInstrumentResponse } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import FilterPanel from './StockPool/FilterPanel.vue';
import { PoolService } from '@/api';
import { Misc, Utils } from '@/script';

let interval = 0;

// 筛选状态
const filterStatus = ref(2); // 默认停止状态

// 股票池数据
const poolStocks = shallowRef<PoolStock[]>([]);

// 选中的股票值
const selectedStock = shallowRef('');

// 远程搜索结果
const searchResults = shallowRef<QueryInstrumentResponse[]>([]);

// 搜索加载状态
const searchLoading = shallowRef(false);

// 表格列定义
const columns: ColumnDefinition<PoolStock> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '股票代码',
    width: 200,
    sortable: true,
    cellRenderer: ({ rowData }) => (
      <span class={rowData.is_limit ? 'c-[var(--g-orange)]' : ''}>{rowData.instrument}</span>
    ),
  },
  {
    key: 'instrument_name',
    dataKey: 'instrument_name',
    title: '股票名称',
    width: 200,
    sortable: true,
    cellRenderer: ({ rowData }) => (
      <span class={rowData.is_limit ? 'c-[var(--g-orange)]' : ''}>{rowData.instrument_name}</span>
    ),
  },
  {
    key: 'pct',
    dataKey: 'pct',
    title: '当前涨幅',
    width: 200,
    sortable: true,
    cellRenderer: ({ cellData }) => (
      <span
        class={
          cellData > 0 ? 'c-[var(--g-red)]' : cellData === 0 ? 'text-white' : 'c-[var(--g-green)]'
        }
      >
        {cellData ? cellData + '%' : cellData === 0 ? '0.00%' : ''}
      </span>
    ),
  },
  {
    key: 'avg_pct',
    dataKey: 'avg_pct',
    title: '均价涨幅',
    width: 200,
    sortable: true,
    cellRenderer: ({ cellData }) => (
      <span
        class={
          cellData > 0 ? 'c-[var(--g-red)]' : cellData === 0 ? 'text-white' : 'c-[var(--g-green)]'
        }
      >
        {cellData ? cellData + '%' : cellData === 0 ? '0.00%' : ''}
      </span>
    ),
  },
  {
    key: 'join_time',
    dataKey: 'join_time',
    title: '加入时间',
    width: 200,
    sortable: true,
    cellRenderer: ({ cellData }) => <span>{cellData?.slice(0, 19)}</span>,
  },
  {
    key: 'is_ok',
    dataKey: 'is_ok',
    title: '实时情况',
    width: 200,
    sortable: true,
    cellRenderer: ({ cellData }) => (
      <span class={!cellData ? 'c-[var(--g-red)]' : 'c-[var(--g-green)]'}>
        {cellData ? '满足条件' : '不满足条件'}
      </span>
    ),
  },
  {
    key: 'join_type',
    dataKey: 'join_type',
    title: '入池类型',
    width: 100,
    sortable: true,
    show: Misc.isAdmin(),
    cellRenderer: ({ cellData }) => <span>{cellData === 1 ? '自动' : '手动'}</span>,
  },
];

// 行操作定义
const rowActions: RowAction<PoolStock>[] = [
  {
    label: '删除',
    color: 'var(--g-red)',
    onClick: handleDelete,
  },
];

// 封板率
const boardRate = computed(() => {
  return Utils.formatNumber((poolStocks.value.filter(item => item.is_limit).length / poolStocks.value.length) * 100, {
    fix: 2,
    percent: true,
  });
})

// 获取股票池数据
const getPoolStocks = async () => {
  const { error_code, error_msg, data } = await PoolService.get();
  if (error_code === 0 && data) {
    poolStocks.value = data;
  } else {
    console.error('获取股票池数据失败:', error_msg);
    // ElMessage.error(error_msg || '获取股票池数据失败');
  }
};

// 远程搜索股票
const remoteSearch = async (query: string) => {
  if (!query.trim()) {
    searchResults.value = [];
    return;
  }

  searchLoading.value = true;
  try {
    const { error_code, error_msg, data } = await PoolService.queryInstrument(query);
    if (error_code === 0 && data) {
      searchResults.value = data;
    } else {
      searchResults.value = [];
      ElMessage.error(error_msg || '搜索股票失败');
    }
  } catch (error) {
    console.error('搜索股票失败:', error);
    searchResults.value = [];
    ElMessage.error('搜索股票失败');
  } finally {
    searchLoading.value = false;
  }
};

// 选择股票并手动入池
const handleStockSelect = async (value: string) => {
  const stock = searchResults.value.find(item => item.stock_code === value);
  if (!stock) return;

  const { error_code, error_msg, data } = await PoolService.add({
    instrument: stock.stock_code,
    instrument_name: stock.stock_name,
  });

  if (error_code === 0 && data) {
    ElMessage.success('股票入池成功');
    // 更新本地数据
    Misc.putRow(data, poolStocks);
    // 清空搜索
    selectedStock.value = '';
    searchResults.value = [];
  } else {
    ElMessage.error(error_msg || '股票入池失败');
  }
};

// 删除股票
async function handleDelete(rowData: PoolStock) {
  try {
    await ElMessageBox.confirm(
      `确定要删除股票 ${rowData.instrument_name}(${rowData.instrument}) 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    const { error_code, error_msg } = await PoolService.delete(rowData.id);
    if (error_code === 0) {
      ElMessage.success('删除成功');
      // 从本地数据中移除
      poolStocks.value = poolStocks.value.filter(item => item.id !== rowData.id);
    } else {
      ElMessage.error(error_msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除股票失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

// 格式化搜索结果用于虚拟化选择器
const formatSearchOptions = () => {
  return searchResults.value.map(item => ({
    label: `${item.stock_code} ${item.stock_name}`,
    value: item.stock_code,
    instrument_name: item.stock_name,
  }));
};

// 组件挂载时获取数据
onMounted(() => {
  getPoolStocks();
  interval = setInterval(() => {
    getPoolStocks();
  }, 3000);
  window.addEventListener('keydown', handleF2Key);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleF2Key);
  clearInterval(interval);
});

// 清空股票池
const clearPool = async () => {
  try {
    await ElMessageBox.confirm('确定要清空股票池吗？', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    const { error_code, error_msg } = await PoolService.clear();
    if (error_code === 0) {
      ElMessage.success('清空成功');
      getPoolStocks();
    } else {
      ElMessage.error(error_msg || '清空失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空股票池失败:', error);
      ElMessage.error('清空失败');
    }
  }
};

// F2 按键处理函数
const handleF2Key = async (e: KeyboardEvent) => {
  if (e.key === 'F2') {
    const instruments = poolStocks.value.map(item => item.instrument);
    const { error_code, error_msg } = await PoolService.sendInstruments(instruments);
    if (error_code === 0) {
      ElMessage.success('发送成功');
    } else if (error_code === -1) {
      ElMessage.warning('发送失败，请检查交易软件是否启动');
    } else {
      ElMessage.error(error_msg || '发送失败');
    }
  }
};
</script>

<template>
  <div flex="~ col">
    <!-- 筛选条件板块 -->
    <FilterPanel @status-change="status => (filterStatus = status || 2)" />
    <VirtualizedTable
      flex-1
      min-h-1
      show-index
      :columns="columns"
      :data="poolStocks"
      :row-actions="rowActions"
      :row-action-width="80"
    >
      <template #toolbar>
        <!-- 只有具有手动入池权限的用户才能看到搜索框 -->
        <div v-if="Misc.hasPoolPermission()" flex aic gap-10>
          <div>手动入池：</div>
          <el-select-v2
            v-model="selectedStock"
            :options="formatSearchOptions()"
            :loading="searchLoading"
            placeholder="搜索股票代码或名称"
            clearable
            filterable
            remote
            :remote-method="remoteSearch"
            class="w-200!"
            @change="handleStockSelect"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-select-v2>
          <!-- <div c-white>
            按
            <span color="[--g-green]">F2</span>
            加入买入监控
          </div> -->
          <div flex aic gap-10>
            <div>封板率</div>
            <div c-white>{{ boardRate }}</div>
          </div>
        </div>
        <div v-else>按F2加入买入监控</div>
        <div class="actions" flex aic jce>
          <el-button
            @click="clearPool"
            size="small"
            :disabled="filterStatus !== 2"
            color="var(--g-danger)"
          >
            清空
          </el-button>
          <el-button @click="getPoolStocks" size="small" color="var(--g-primary)">刷新</el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped></style>
