<script setup lang="ts">
import { watch } from 'vue';

const emit = defineEmits<{
  'status-change': [status?: number];
}>();

import { onMounted, shallowRef, ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { FilterBody, FilterCondition } from '@/types';
import { ScreeningService } from '@/api';
import type { PlateOption } from '@/api/screening';
import { Misc } from '@/script';

// 新增筛选条件配置数组
const filterInputs: {
  label: string;
  key: keyof FilterCondition;
  suffix: string;
  min: number;
  max: number;
  precision?: number;
}[] = [
  { label: '当日涨幅 >', key: 'pct', suffix: '%', min: -20, max: 20, precision: 2 },
  { label: '当日成交金额 >', key: 'volume', suffix: '万', min: 0, max: 1000000000000 },
  { label: '当日成交金额 <', key: 'less_volume', suffix: '万', min: 0, max: 1000000000000 },
  { label: '当日均价涨跌幅 >', key: 'avg_pct', suffix: '%', min: -20, max: 20, precision: 2 },
  { label: '昨日涨幅 <', key: 'pre_pct', suffix: '%', min: -20, max: 20, precision: 2 },
  { label: '近一周涨幅 <', key: 'pre_5_pct', suffix: '%', min: -20, max: 20, precision: 2 },
];

const plateOptions = shallowRef<PlateOption[]>([
  { label: '非st', value: '非st' },
  { label: '非创业板', value: '非创业板' },
  { label: '非北交所', value: '非北证' },
  { label: '非科创板', value: '非科创板' },
]);

const plate = ref<string[]>([]);

// 筛选条件相关状态
const filterCondition = ref<FilterCondition>({});
const currentFilterBody = shallowRef<FilterBody | null>(null);

// 计算属性：判断是否有筛选条件
const hasFilterConditions = computed(() => {
  const condition = filterCondition.value;
  return !!(
    plate.value.length ||
    condition.pct !== undefined ||
    condition.volume !== undefined ||
    condition.less_volume !== undefined ||
    condition.avg_pct !== undefined ||
    condition.pre_pct !== undefined ||
    condition.pre_5_pct !== undefined
  );
});

// 开始按钮是否禁用
const isStartDisabled = computed(() => {
  return !hasFilterConditions.value || currentFilterBody.value?.status === 1;
});

// 停止按钮是否禁用
const isStopDisabled = computed(() => {
  return !currentFilterBody.value || currentFilterBody.value.status === 2;
});

// 禁用表单
const isFormDisabled = computed(() => {
  return currentFilterBody.value?.status === 1;
});

// 获取当前筛选状态
const getFilterBody = async () => {
  try {
    const { error_code, data } = await ScreeningService.get();
    if (error_code === 0 && data && data.id) {
      currentFilterBody.value = data;
      // 解析筛选条件
      if (data.filter_condition) {
        try {
          filterCondition.value = JSON.parse(data.filter_condition);
          plate.value = filterCondition.value.plate?.split(',') || [];
        } catch (e) {
          console.error('解析筛选条件失败:', e);
        }
      }
    } else {
      currentFilterBody.value = null;
    }
  } catch (error) {
    console.error('获取筛选状态失败:', error);
    currentFilterBody.value = null;
  }
};

// 开始筛选
const startScreening = async () => {
  if (!hasFilterConditions.value) {
    ElMessage.warning('请至少填写一个筛选条件');
    return;
  }

  try {
    // 构建筛选条件对象，只包含有值的字段
    const condition: FilterCondition = {};
    if (plate.value.length) condition.plate = plate.value.join(',');
    if (filterCondition.value.pct !== undefined) condition.pct = filterCondition.value.pct;
    if (filterCondition.value.volume !== undefined) condition.volume = filterCondition.value.volume;
    if (filterCondition.value.less_volume !== undefined)
      condition.less_volume = filterCondition.value.less_volume;
    if (filterCondition.value.avg_pct !== undefined)
      condition.avg_pct = filterCondition.value.avg_pct;
    if (filterCondition.value.pre_pct !== undefined)
      condition.pre_pct = filterCondition.value.pre_pct;
    if (filterCondition.value.pre_5_pct !== undefined)
      condition.pre_5_pct = filterCondition.value.pre_5_pct;

    const { error_code, error_msg, data } = await ScreeningService.start({
      user_id: Misc.getUser()?.id || 0,
      user_name: Misc.getUser()?.user_name || '',
      filter_condition: JSON.stringify(condition),
      status: 1,
      id: currentFilterBody.value?.id,
    });

    if (error_code === 0 && data) {
      ElMessage.success('筛选已开始');
      currentFilterBody.value = data;
    } else {
      ElMessage.error(error_msg || '开始筛选失败');
    }
  } catch (error) {
    console.error('开始筛选失败:', error);
    ElMessage.error('开始筛选失败');
  } finally {
  }
};

// 停止筛选
const stopScreening = async () => {
  if (!currentFilterBody.value) return;

  try {
    const { error_code, error_msg, data } = await ScreeningService.stop(currentFilterBody.value);
    if (error_code === 0 && data) {
      ElMessage.success('筛选已停止');
      currentFilterBody.value = data;
    } else {
      ElMessage.error(error_msg || '停止筛选失败');
    }
  } catch (error) {
    console.error('停止筛选失败:', error);
    ElMessage.error('停止筛选失败');
  } finally {
  }
};

// 组件挂载时获取数据
onMounted(() => {
  getFilterBody();
});

// 监听筛选状态变化
watch(
  () => currentFilterBody.value?.status,
  status => {
    emit('status-change', status);
  },
);

// 监听筛选状态变化
watch(
  () => currentFilterBody.value?.status,
  status => {
    emit('status-change', status);
  },
);
</script>

<template>
  <!-- 筛选条件板块 - 只有管理员可见 -->
  <div v-if="Misc.isAdmin()" px-20 py-10 mb-10 bg="[--g-panel-bg]">
    <div mb-12 fs-16 fw-600 c-white>筛选条件</div>
    <div flex gap-20 flex-wrap>
      <!-- 排除板块 -->
      <div flex aic gap-10>
        <div class="label" fs-14 c="[--g-text-secondary]">排除板块</div>
        <el-checkbox-group :disabled="isFormDisabled" flex-1 min-w-1 v-model="plate">
          <el-checkbox
            v-for="item in plateOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-checkbox>
        </el-checkbox-group>
      </div>

      <!-- 其他筛选条件 -->
      <div v-for="input in filterInputs" :key="input.key" flex aic gap-10>
        <div class="label" fs-14 c="[--g-text-secondary]">{{ input.label }}</div>
        <el-input-number
          flex-1
          min-w-1
          v-model="filterCondition[input.key]"
          :disabled="isFormDisabled"
          :controls="false"
          :min="input.min"
          :max="input.max"
          :precision="input.precision"
          placeholder="请输入"
        >
          <template #suffix>
            <span>{{ input.suffix }}</span>
          </template>
        </el-input-number>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div mt-16 flex aic gap-12>
      <el-button size="small" type="primary" :disabled="isStartDisabled" @click="startScreening">
        开始
      </el-button>
      <el-button size="small" type="danger" :disabled="isStopDisabled" @click="stopScreening">
        停止
      </el-button>
      <div v-if="currentFilterBody" ml-16 fs-14>
        <span c="[--g-text-secondary]">当前状态：</span>
        <span :class="currentFilterBody.status === 1 ? 'c-green' : 'c-red'">
          {{ currentFilterBody.status === 1 ? '运行中' : '已停止' }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
